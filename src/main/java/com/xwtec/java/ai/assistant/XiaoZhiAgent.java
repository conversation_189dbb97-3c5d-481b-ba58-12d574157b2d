package com.xwtec.java.ai.assistant;

import dev.langchain4j.service.MemoryId;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.spring.AiService;
import dev.langchain4j.service.spring.AiServiceWiringMode;

@AiService(
        wiringMode = AiServiceWiringMode.EXPLICIT,
        chatModel = "qwenChatModel",
        chatMemoryProvider = "chatMemoryProviderXiaoZhi",
        tools = "appointmentTools"
//        , contentRetriever = "contentRetrieverXiaoZhi"
)
public interface XiaoZhiAgent {

    @SystemMessage(fromResource = "prompt-xiaozhi-tpl.txt")
    String chat(@MemoryId Long memoryId, @UserMessage String userMessage);
}
