package com.xwtec.java.ai.config;

import com.xwtec.java.ai.store.MongoChatMemoryStore;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.loader.FileSystemDocumentLoader;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.memory.ChatMemory;
import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.store.embedding.EmbeddingStoreIngestor;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

@Configuration
public class ChatMemoryConfig {

    @Autowired
    private MongoChatMemoryStore chatMemoryStore;

    // 配置消息窗口的聊天记忆
    @Bean
    public ChatMemory chatMemory() {
         return MessageWindowChatMemory.withMaxMessages(10);
    }

    @Bean
    public ChatMemoryProvider chatMemoryProvider() {
        return memoryId -> MessageWindowChatMemory
                .builder()
                .id(memoryId)
                .maxMessages(10)
                .chatMemoryStore(chatMemoryStore)
                .build();
    }

    @Bean
    public ChatMemoryProvider chatMemoryProviderXiaoZhi() {
        return memoryId -> MessageWindowChatMemory
                .builder()
                .id(memoryId)
                .maxMessages(20)
                .chatMemoryStore(chatMemoryStore)
                .build();
    }

    @Bean
    public ContentRetriever contentRetrieverXiaoZhi() {
        Document document1 = FileSystemDocumentLoader.loadDocument("/Users/<USER>/temp2025/rag/yy.md");
        Document document2 = FileSystemDocumentLoader.loadDocument("/Users/<USER>/temp2025/rag/keshi.md");
        Document document3 = FileSystemDocumentLoader.loadDocument("/Users/<USER>/temp2025/rag/sjnk.md");
        List<Document> documentList = Arrays.asList(document1, document2, document3);
        InMemoryEmbeddingStore<TextSegment> embeddingStore = new InMemoryEmbeddingStore<>();
        EmbeddingStoreIngestor.ingest(documentList, embeddingStore);
        return EmbeddingStoreContentRetriever.from(embeddingStore);
    }
}
